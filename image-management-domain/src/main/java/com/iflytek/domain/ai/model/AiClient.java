package com.iflytek.domain.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiClient {

    private Long clientId;

    private String systemPrompt;

    private AiClientModel model;

    private List<AiClientToolMcp> mcpServiceList;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AiClientModel {

        /**
         * 主键ID
         */
        private Long id;

        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 基础URL
         */
        private String baseUrl;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * 完成路径
         */
        private String completionsPath;

        /**
         * 嵌入路径
         */
        private String embeddingsPath;

        /**
         * 模型类型(openai/azure等)
         */
        private String modelType;

        /**
         * 模型版本
         */
        private String modelVersion;

        /**
         * 超时时间(秒)
         */
        private Integer timeout;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AiClientToolMcp {

        /**
         * 主键ID
         */
        private Long id;

        /**
         * MCP名称
         */
        private String mcpName;

        /**
         * 传输类型(sse/stdio)
         */
        private String transportType;

        /**
         * 传输配置 - sse
         */
        private TransportConfigSse transportConfigSse;

        /**
         * 传输配置 - stdio
         */
        private TransportConfigStdio transportConfigStdio;

        /**
         * 请求超时时间(分钟)
         */
        private Integer requestTimeout;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class TransportConfigSse {
            private String baseUri;
            private String sseEndpoint;
        }

        /**
         * "mcp-server-weixin": {
         * "command": "java",
         * "args": [
         * "-Dspring.ai.mcp.server.stdio=true",
         * "-jar",
         * "/Users/<USER>/Applications/apache-maven-3.8.4/repository/cn/bugstack/mcp/mcp-server-weixin/1.0.0/mcp-server-weixin-1.0.0.jar"
         * ]
         */
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class TransportConfigStdio {

            private Map<String, Stdio> stdio;

            @Data
            public static class Stdio {
                private String command;
                private List<String> args;
            }
        }

    }


}
