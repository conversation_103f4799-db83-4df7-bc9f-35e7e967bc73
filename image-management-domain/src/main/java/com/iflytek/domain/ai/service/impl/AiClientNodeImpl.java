package com.iflytek.domain.ai.service.impl;

import com.iflytek.domain.ai.model.AiClient;
import com.iflytek.domain.ai.service.AiClientNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.mcp.client.McpClient;
import org.springframework.ai.mcp.client.stdio.ServerParameters;
import org.springframework.ai.mcp.client.stdio.StdioClientTransport;
import org.springframework.ai.mcp.client.sse.SseClientTransport;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AiClientNodeImpl implements Ai<PERSON>lientNode {

    public ChatClient createChatClient(AiClient aiClient) {
        // 创建聊天模型
        ChatModel chatModel = createChatModel(aiClient.getModel());
        
        // 构建ChatClient
        ChatClient.Builder builder = ChatClient.builder(chatModel);
        
        // 配置系统提示词
        if (aiClient.getSystemPrompt() != null && !aiClient.getSystemPrompt().isEmpty()) {
            builder.defaultSystem(aiClient.getSystemPrompt());
        }
        
        // 配置MCP工具
        if (aiClient.getMcpServiceList() != null && !aiClient.getMcpServiceList().isEmpty()) {
            List<McpClient> mcpClients = createMcpClients(aiClient.getMcpServiceList());
            // 注册MCP工具到ChatClient
            for (McpClient mcpClient : mcpClients) {
                builder.defaultFunctions(mcpClient.listTools().stream()
                    .map(tool -> tool.getName())
                    .toArray(String[]::new));
            }
        }
        
        return builder.build();
    }
    
    private ChatModel createChatModel(AiClient.AiClientModel model) {
        log.info("创建聊天模型: {}", model.getModelName());
        
        OpenAiApi openAiApi = new OpenAiApi(
            model.getBaseUrl(),
            model.getApiKey()
        );
        
        return OpenAiChatModel.builder()
            .openAiApi(openAiApi)
            .model(model.getModelName())
            .build();
    }
    
    private List<McpClient> createMcpClients(List<AiClient.AiClientToolMcp> mcpServices) {
        List<McpClient> mcpClients = new ArrayList<>();
        
        for (AiClient.AiClientToolMcp mcpService : mcpServices) {
            try {
                McpClient mcpClient = createMcpClient(mcpService);
                mcpClients.add(mcpClient);
                log.info("成功创建MCP客户端: {}", mcpService.getMcpName());
            } catch (Exception e) {
                log.error("创建MCP客户端失败: {}", mcpService.getMcpName(), e);
            }
        }
        
        return mcpClients;
    }
    
    private McpClient createMcpClient(AiClient.AiClientToolMcp mcpService) {
        if ("stdio".equals(mcpService.getTransportType())) {
            return createStdioMcpClient(mcpService);
        } else if ("sse".equals(mcpService.getTransportType())) {
            return createSseMcpClient(mcpService);
        } else {
            throw new IllegalArgumentException("不支持的传输类型: " + mcpService.getTransportType());
        }
    }
    
    private McpClient createStdioMcpClient(AiClient.AiClientToolMcp mcpService) {
        AiClient.AiClientToolMcp.TransportConfigStdio stdioConfig = mcpService.getTransportConfigStdio();
        
        for (Map.Entry<String, AiClient.AiClientToolMcp.TransportConfigStdio.Stdio> entry : 
             stdioConfig.getStdio().entrySet()) {
            
            AiClient.AiClientToolMcp.TransportConfigStdio.Stdio stdio = entry.getValue();
            
            ServerParameters serverParams = ServerParameters.builder(stdio.getCommand())
                .arguments(stdio.getArgs())
                .build();
            
            StdioClientTransport transport = new StdioClientTransport(serverParams);
            
            return McpClient.using(transport)
                .requestTimeout(java.time.Duration.ofMinutes(mcpService.getRequestTimeout()))
                .sync();
        }
        
        throw new IllegalArgumentException("Stdio配置为空");
    }
    
    private McpClient createSseMcpClient(AiClient.AiClientToolMcp mcpService) {
        AiClient.AiClientToolMcp.TransportConfigSse sseConfig = mcpService.getTransportConfigSse();
        
        SseClientTransport transport = new SseClientTransport(
            sseConfig.getBaseUri() + sseConfig.getSseEndpoint()
        );
        
        return McpClient.using(transport)
            .requestTimeout(java.time.Duration.ofMinutes(mcpService.getRequestTimeout()))
            .sync();
    }
}