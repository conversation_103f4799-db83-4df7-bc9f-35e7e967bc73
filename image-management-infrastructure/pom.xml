<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek</groupId>
        <artifactId>image-management</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>image-management-infrastructure</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- 系统模块 -->
        <dependency>
            <groupId>com.iflytek</groupId>
            <artifactId>image-management-domain</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>image-management-infrastructure</finalName>
    </build>

</project>
